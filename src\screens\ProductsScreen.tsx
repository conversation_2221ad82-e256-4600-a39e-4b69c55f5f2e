import React, { useState, useEffect } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { useData } from '../contexts/DataContext';
import { Product, Canvas } from '../types';

interface ProductsScreenProps {
  onNavigate?: (screen: string) => void;
  searchTerm?: string;
  showAddModal?: boolean;
  onCloseAddModal?: () => void;
  onProductSelect?: (product: Product) => void;
}

const ProductsScreen: React.FC<ProductsScreenProps> = ({
  onNavigate,
  searchTerm: propSearchTerm = '',
  showAddModal = false,
  onCloseAddModal,
  onProductSelect
}) => {
  const { isDark } = useTheme();
  const { state, saveProduct, updateProduct, deleteProduct, saveCanvas, updateCurrentCanvas, setCurrentCanvas } = useData();
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [productToDelete, setProductToDelete] = useState<Product | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    category: '',
    subcategory: '',
    description: '',
    quantity: 1,
    image: '',
    price: '',
    material: '',
  });

  const [imageInputMethod, setImageInputMethod] = useState<'upload' | 'camera' | 'url'>('upload');
  const [imagePreview, setImagePreview] = useState<string>('');
  const [imageFile, setImageFile] = useState<File | null>(null);

  const isModalVisible = showAddModal || editingProduct !== null;

  useEffect(() => {
    if (editingProduct) {
      setFormData({
        name: editingProduct.name,
        category: editingProduct.category,
        subcategory: editingProduct.subcategory,
        description: editingProduct.description || '',
        quantity: editingProduct.quantity,
        image: editingProduct.image || '',
        price: editingProduct.price || '',
        material: editingProduct.material || '',
      });
      setImagePreview(editingProduct.image || '');
    } else {
      resetForm();
    }
  }, [editingProduct]);

  const resetForm = () => {
    setFormData({
      name: '',
      category: '',
      subcategory: '',
      description: '',
      quantity: 1,
      image: '',
      price: '',
      material: '',
    });
    setImagePreview('');
    setImageFile(null);
    setImageInputMethod('upload');
  };

  const handleSave = async () => {
    console.log('ProductsScreen.handleSave called with formData:', formData);

    if (!formData.name.trim() || !formData.category.trim()) {
      alert('Error: Name and category are required');
      return;
    }

    try {
      if (editingProduct) {
        console.log('Updating existing product:', editingProduct.id);
        // Update existing product
        await updateProduct({
          ...editingProduct,
          ...formData,
          price: formData.price === '' ? undefined : Number(formData.price),
          material: formData.material || undefined,
        });
      } else {
        console.log('Creating new product');
        // Create new product
        const newProduct = {
          id: Date.now().toString(),
          ...formData,
          price: formData.price === '' ? undefined : Number(formData.price),
          material: formData.material || undefined,
          position: { x: 0, y: 0 },
          rotation: 0,
        };
        console.log('New product object:', newProduct);
        await saveProduct(newProduct);
      }

      if (showAddModal) onCloseAddModal && onCloseAddModal();
      setEditingProduct(null);
      resetForm();
    } catch (error) {
      console.error('Error in handleSave:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to save product';
      alert(`Error: ${errorMessage}`);
    }
  };

  const handleDelete = (product: Product) => {
    setProductToDelete(product);
    setShowDeleteConfirm(true);
  };

  const confirmDelete = () => {
    if (productToDelete) {
      deleteProduct(productToDelete.id).then(() => {
        setShowDeleteConfirm(false);
        setProductToDelete(null);
      }).catch(() => {
        alert('Error: Failed to delete product');
        setShowDeleteConfirm(false);
        setProductToDelete(null);
      });
    }
  };

  const cancelDelete = () => {
    setShowDeleteConfirm(false);
    setProductToDelete(null);
  };

  const handleAddToCanvas = async (product: Product) => {
    try {
      let canvasId: string;
      let canvasName: string;
      let existingProducts: any[] = [];
      let existingConnections: any[] = [];

      if (state.currentCanvas) {
        canvasId = state.currentCanvas.id;
        canvasName = state.currentCanvas.name;
        existingProducts = state.currentCanvas.products || [];
        existingConnections = state.currentCanvas.connections || [];
      } else {
        // Create a default canvas if none exists
        canvasId = 'default';
        canvasName = 'My Canvas';
        existingProducts = [];
        existingConnections = [];
      }

      const newCanvasProduct = {
        ...product,
        id: `${canvasId}_${product.id}`, // Unique ID for canvas-product relationship
        position: { x: 0, y: 0 },
        canvasId,
      };

      const updatedProducts = [...existingProducts, newCanvasProduct];
      const updatedCanvas = {
        id: canvasId,
        name: canvasName,
        products: updatedProducts,
        connections: existingConnections,
      };

      await saveCanvas(updatedCanvas);

      // If there was no current canvas, set this as the current one
      if (!state.currentCanvas) {
        const fullCanvas: Canvas = {
          ...updatedCanvas,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        setCurrentCanvas(fullCanvas);
      }
    } catch (error) {
      alert('Error: Failed to add product to canvas');
      console.error('Error adding product:', error);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setImageFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setImagePreview(result);
        setFormData({ ...formData, image: result });
      };
      reader.readAsDataURL(file);
    }
  };

  const handleCameraCapture = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.capture = 'environment'; // Use back camera on mobile
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        setImageFile(file);
        const reader = new FileReader();
        reader.onload = (e) => {
          const result = e.target?.result as string;
          setImagePreview(result);
          setFormData({ ...formData, image: result });
        };
        reader.readAsDataURL(file);
      }
    };
    input.click();
  };

  const handleUrlInput = (url: string) => {
    setFormData({ ...formData, image: url });
    setImagePreview(url);
    setImageFile(null);
  };

  const removeImage = () => {
    setFormData({ ...formData, image: '' });
    setImagePreview('');
    setImageFile(null);
  };

  // Filter products based on search term with multi-word support
  const filteredProducts = state.products.filter(product => {
    if (!propSearchTerm.trim()) return true;

    const searchWords = propSearchTerm.toLowerCase().trim().split(/\s+/);
    const searchableText = [
      product.name,
      product.category,
      product.subcategory,
      product.description || ''
    ].join(' ').toLowerCase();

    // Check if all search words are found in the searchable text
    return searchWords.every((word: string) => searchableText.includes(word));
  });

  // No grouping needed for grid layout - use filteredProducts directly

  const containerStyle: React.CSSProperties = {
    backgroundColor: isDark ? '#1e293b' : '#f9fafb',
    minHeight: '100vh',
    padding: '0',
    maxWidth: '100vw',
    overflowX: 'hidden',
  };

  const headerStyle: React.CSSProperties = {
    backgroundColor: isDark ? '#1e293b' : '#ffffff',
    padding: '12px 16px',
    borderRadius: '0',
    marginBottom: '0',
    display: 'flex',
    flexDirection: 'column',
    gap: '12px',
    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
    position: 'sticky',
    top: '0',
    left: '0',
    right: '0',
    zIndex: 100,
    width: '100vw',
    height: '64px',
    boxSizing: 'border-box',
  };

  const titleStyle: React.CSSProperties = {
    color: isDark ? '#ffffff' : '#000000',
    fontSize: '24px',
    fontWeight: 'bold',
    margin: 0,
    flexShrink: 0,
  };

  const addButtonStyle: React.CSSProperties = {
    backgroundColor: '#3b82f6',
    color: '#ffffff',
    border: 'none',
    padding: '8px 16px',
    borderRadius: '8px',
    cursor: 'pointer',
    fontWeight: '600',
  };

  const menuButtonStyle: React.CSSProperties = {
    backgroundColor: 'transparent',
    color: isDark ? '#ffffff' : '#000000',
    border: 'none',
    padding: '8px',
    borderRadius: '8px',
    cursor: 'pointer',
    fontSize: '18px',
    fontWeight: '600',
  };

  const searchInputStyle: React.CSSProperties = {
    flex: 1,
    padding: '10px 12px',
    borderRadius: '8px',
    border: '1px solid #d1d5db',
    backgroundColor: isDark ? '#4b5563' : '#f3f4f6',
    color: isDark ? '#ffffff' : '#000000',
    fontSize: '14px',
    outline: 'none',
    marginRight: '12px',
    maxWidth: '150px',
  };

  const inputStyle: React.CSSProperties = {
    width: '100%',
    padding: '12px',
    borderRadius: '8px',
    border: '1px solid #d1d5db',
    marginBottom: '12px',
    backgroundColor: isDark ? '#4b5563' : '#f3f4f6',
    color: isDark ? '#ffffff' : '#000000',
    fontSize: '16px',
  };

  const methodButtonStyle: React.CSSProperties = {
    padding: '8px 16px',
    borderRadius: '6px',
    border: '1px solid #d1d5db',
    cursor: 'pointer',
    fontSize: '14px',
    fontWeight: '500',
    flex: 1,
    minWidth: '80px',
  };

  return (
    <>
      <style>
        {`
          /* Hide scrollbar for products screen scrollable area */
          .products-screen-scroll::-webkit-scrollbar {
            display: none;
          }
          .products-screen-scroll {
            -ms-overflow-style: none; /* IE and Edge */
            scrollbar-width: none; /* Firefox */
          }

          .products-row::-webkit-scrollbar {
            height: 4px;
          }
          .products-row::-webkit-scrollbar-track {
            background: ${isDark ? '#374151' : '#f1f5f9'};
            border-radius: 2px;
          }
          .products-row::-webkit-scrollbar-thumb {
            background: ${isDark ? '#6b7280' : '#cbd5e1'};
            border-radius: 2px;
          }
          .products-row::-webkit-scrollbar-thumb:hover {
            background: ${isDark ? '#9ca3af' : '#94a3b8'};
          }

          @media (max-width: 480px) {
            .products-row {
              gap: 8px !important;
            }
            .product-card {
              min-width: 120px !important;
              max-width: 140px !important;
              width: calc(42vw - 16px) !important;
            }
          }

          @media (max-width: 360px) {
            .product-card {
              min-width: 110px !important;
              max-width: 130px !important;
              width: calc(40vw - 16px) !important;
            }
          }

          /* Touch device optimizations */
          @media (hover: none) and (pointer: coarse) {
            .product-card {
              cursor: default !important;
            }
            .products-row {
              -webkit-overflow-scrolling: touch;
              scroll-behavior: auto;
            }
          }

          /* Landscape orientation adjustments */
          @media (max-height: 500px) and (orientation: landscape) {
            .product-card {
              padding: 8px !important;
            }
            .product-card img {
              height: 80px !important;
            }
          }

          /* Sticky header optimizations */
          .sticky-header {
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            background-color: ${isDark ? 'rgba(30, 41, 59, 0.95)' : 'rgba(255, 255, 255, 0.95)'};
          }

          @supports not (backdrop-filter: blur(10px)) {
            .sticky-header {
              background-color: ${isDark ? '#1e293b' : '#ffffff'};
            }
          }

          /* Mobile header adjustments */
          @media (max-width: 480px) {
            .sticky-header {
              padding: '10px 16px' !important;
              height: '56px' !important;
            }
          }

          @media (max-width: 360px) {
            .sticky-header {
              padding: '8px 16px' !important;
              height: '52px' !important;
            }
          }
        `}
      </style>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 10000,
        }}>
          <div style={{
            backgroundColor: isDark ? '#374151' : '#ffffff',
            borderRadius: '12px',
            padding: '24px',
            maxWidth: '400px',
            width: '90%',
            boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
            border: `1px solid ${isDark ? '#4b5563' : '#e5e7eb'}`,
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              marginBottom: '16px',
            }}>
              <div style={{
                width: '48px',
                height: '48px',
                borderRadius: '50%',
                backgroundColor: '#fef2f2',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: '16px',
              }}>
                <span style={{
                  fontSize: '24px',
                  color: '#ef4444',
                }}>⚠️</span>
              </div>
              <div>
                <h3 style={{
                  margin: 0,
                  fontSize: '18px',
                  fontWeight: '600',
                  color: isDark ? '#ffffff' : '#111827',
                }}>
                  Delete Product
                </h3>
                <p style={{
                  margin: '4px 0 0 0',
                  fontSize: '14px',
                  color: isDark ? '#9ca3af' : '#6b7280',
                }}>
                  This action cannot be undone
                </p>
              </div>
            </div>

            <p style={{
              margin: '0 0 24px 0',
              fontSize: '14px',
              color: isDark ? '#d1d5db' : '#374151',
              lineHeight: '1.5',
            }}>
              Are you sure you want to delete "<strong>{productToDelete?.name}</strong>"? This will permanently remove the product from your inventory.
            </p>

            <div style={{
              display: 'flex',
              gap: '12px',
              justifyContent: 'flex-end',
            }}>
              <button
                onClick={cancelDelete}
                style={{
                  backgroundColor: 'transparent',
                  color: isDark ? '#9ca3af' : '#6b7280',
                  border: `1px solid ${isDark ? '#4b5563' : '#d1d5db'}`,
                  padding: '8px 16px',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: '500',
                  transition: 'all 0.2s ease',
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = isDark ? '#374151' : '#f9fafb';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
              >
                Cancel
              </button>
              <button
                onClick={confirmDelete}
                style={{
                  backgroundColor: '#ef4444',
                  color: '#ffffff',
                  border: 'none',
                  padding: '8px 16px',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: '500',
                  transition: 'all 0.2s ease',
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#dc2626';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = '#ef4444';
                }}
              >
                Delete Product
              </button>
            </div>
          </div>
        </div>
      )}

      <div style={containerStyle} className="products-screen">

      {/* Scrollable Content Area */}
      <div
        className="products-screen-scroll"
        style={{
          overflowY: 'auto',
          height: 'calc(100vh - 64px)', // Account only for sticky header (64px)
          WebkitOverflowScrolling: 'touch', // Smooth scrolling on iOS
        }}
      >
        {/* Products List */}
      {filteredProducts.length === 0 ? (
        <div style={{
          textAlign: 'center',
          padding: '60px 20px',
          color: isDark ? '#9ca3af' : '#6b7280'
        }}>
          <h2 style={{ fontSize: '48px', marginBottom: '16px' }}>📦</h2>
          <h3 style={{
            margin: '0 0 8px 0',
            color: isDark ? '#ffffff' : '#000000',
            fontSize: '24px'
          }}>
            No Products Yet
          </h3>
          <p style={{ fontSize: '16px', marginBottom: '20px' }}>
            Click "Add Product" to create your first product
          </p>
          <p style={{ fontSize: '14px' }}>
            💡 Products will be displayed as square cards with images and titles
          </p>
        </div>
      ) : (
        <div style={{ padding: '16px' }}>
          {/* Products Grid */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill, minmax(140px, 1fr))',
            gap: '12px',
            width: '100%',
          }}
          className="products-grid"
          >
            {filteredProducts.map((product) => (
              <div
                key={product.id}
                draggable={true}
                onDragStart={(e) => {
                  e.dataTransfer.setData('application/json', JSON.stringify(product));
                  e.dataTransfer.effectAllowed = 'copy';
                }}
                className="product-card"
                style={{
                  backgroundColor: isDark ? '#374151' : '#ffffff',
                  borderRadius: '8px',
                  padding: '12px',
                  border: `1px solid ${isDark ? '#4b5563' : '#e5e7eb'}`,
                  cursor: 'grab',
                  transition: 'transform 0.2s ease',
                  aspectRatio: '1', // Make cards square
                  display: 'flex',
                  flexDirection: 'column',
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-2px)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                }}
              >
                {/* Product Image */}
                {product.image ? (
                  <img
                    src={product.image}
                    alt={product.name}
                    style={{
                      width: '100%',
                      height: '100px',
                      objectFit: 'contain',
                      borderRadius: '4px',
                      marginBottom: '8px',
                      backgroundColor: isDark ? '#4b5563' : '#f9fafb',
                    }}
                  />
                ) : (
                  <div
                    style={{
                      width: '100%',
                      height: '100px',
                      backgroundColor: isDark ? '#4b5563' : '#e5e7eb',
                      borderRadius: '4px',
                      marginBottom: '8px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '32px',
                    }}
                  >
                    📦
                  </div>
                )}

                {/* Product Name */}
                <h3 style={{
                  color: isDark ? '#ffffff' : '#000000',
                  margin: '0 0 6px 0',
                  fontSize: '14px',
                  fontWeight: '600',
                  lineHeight: '1.3',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}>
                  {product.name}
                </h3>

                {/* Product Details */}
                <p style={{
                  color: isDark ? '#9ca3af' : '#6b7280',
                  margin: '0 0 4px 0',
                  fontSize: '11px',
                  lineHeight: '1.3',
                }}>
                  {product.subcategory}
                </p>

                <p style={{
                  color: isDark ? '#d1d5db' : '#4b5563',
                  margin: '0 0 6px 0',
                  fontSize: '10px',
                  lineHeight: '1.3',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}>
                  Price: {product.price !== undefined && product.price !== null && product.price !== '' ? product.price : '-'}
                </p>
                <p style={{
                  color: isDark ? '#d1d5db' : '#4b5563',
                  margin: '0 0 6px 0',
                  fontSize: '10px',
                  lineHeight: '1.3',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}>
                  Material: {product.material ? product.material : 'N/A'}
                </p>
                <p style={{
                  color: isDark ? '#60a5fa' : '#3b82f6',
                  margin: '0 0 8px 0',
                  fontSize: '11px',
                  fontWeight: '500',
                }}>
                  Qty: {product.quantity}
                </p>

                {/* Action Buttons */}
                <div style={{
                  display: 'flex',
                  gap: '4px',
                  width: '100%'
                }}>
                  <button
                    style={{
                      backgroundColor: '#f59e0b',
                      color: '#ffffff',
                      border: 'none',
                      padding: '4px 8px',
                      borderRadius: '3px',
                      cursor: 'pointer',
                      fontWeight: '600',
                      fontSize: '10px',
                      flex: '1 1 0%',
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      setEditingProduct(product);
                    }}
                  >
                    Edit
                  </button>

                  <button
                    style={{
                      backgroundColor: '#10b981',
                      color: '#ffffff',
                      border: 'none',
                      padding: '4px 8px',
                      borderRadius: '3px',
                      cursor: 'pointer',
                      fontWeight: '600',
                      fontSize: '10px',
                      flex: '1 1 0%',
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAddToCanvas(product);
                    }}
                  >
                    Add
                  </button>

                  <button
                    style={{
                      backgroundColor: '#ef4444',
                      color: '#ffffff',
                      border: 'none',
                      padding: '4px 8px',
                      borderRadius: '3px',
                      cursor: 'pointer',
                      fontWeight: '600',
                      fontSize: '10px',
                      flex: '1 1 0%',
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDelete(product);
                    }}
                  >
                    Delete
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}



      {/* Add/Edit Product Modal */}
      {isModalVisible && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          width: '100vw',
          height: '100vh',
          background: isDark
            ? 'linear-gradient(135deg, #232946 0%, #1e293b 100%)'
            : 'linear-gradient(135deg, #f3f4f6 0%, #e0e7ef 100%)',
          zIndex: 2000,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          padding: 0,
        }}>
          <div
            style={{
              background: isDark ? 'rgba(36, 41, 54, 0.98)' : 'rgba(255,255,255,0.98)',
              borderRadius: 24,
              boxShadow: isDark
                ? '0 8px 32px rgba(0,0,0,0.45)'
                : '0 8px 32px rgba(100,116,139,0.18)',
              padding: '32px 16px',
              width: '100%',
              maxWidth: 440,
              maxHeight: '95vh',
              overflowY: 'auto',
              position: 'relative',
              margin: '0 auto',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'flex-start',
              boxSizing: 'border-box',
            }}
          >
            <button
              onClick={() => {
                if (showAddModal) onCloseAddModal && onCloseAddModal();
                setEditingProduct(null);
                resetForm();
              }}
              style={{
                position: 'absolute',
                top: 18,
                right: 18,
                background: isDark ? '#232946' : '#e0e7ef',
                border: 'none',
                borderRadius: '50%',
                width: 40,
                height: 40,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: 22,
                color: isDark ? '#fff' : '#232946',
                cursor: 'pointer',
                boxShadow: isDark ? '0 1px 4px #23294633' : '0 1px 4px #cbd5e133',
                zIndex: 10,
                transition: 'background 0.2s',
              }}
              title="Close"
              type="button"
            >
              ×
            </button>
            <h2 style={{
              color: isDark ? '#fff' : '#232946',
              margin: '0 0 28px 0',
              textAlign: 'center',
              fontWeight: 800,
              fontSize: 28,
              letterSpacing: 0.5,
            }}>
              {editingProduct ? 'Edit Product' : 'Add Product'}
            </h2>

            <div style={{ width: '100%', maxWidth: '100%', marginBottom: 28, boxSizing: 'border-box' }}>
              <input
                type="text"
                placeholder="Product Name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                style={{
                  width: '100%',
                  maxWidth: '100%',
                  padding: '16px',
                  borderRadius: 12,
                  border: `2px solid ${isDark ? '#4b5563' : '#cbd5e1'}`,
                  marginBottom: 16,
                  background: isDark ? '#232946' : '#f8fafc',
                  color: isDark ? '#fff' : '#232946',
                  fontSize: 17,
                  fontWeight: 500,
                  outline: 'none',
                  boxShadow: isDark ? '0 1px 4px #23294633' : '0 1px 4px #cbd5e133',
                  transition: 'border 0.2s',
                  boxSizing: 'border-box',
                }}
              />
              <input
                type="text"
                placeholder="Category"
                value={formData.category}
                onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                style={{
                  width: '100%',
                  maxWidth: '100%',
                  padding: '16px',
                  borderRadius: 12,
                  border: `2px solid ${isDark ? '#4b5563' : '#cbd5e1'}`,
                  marginBottom: 16,
                  background: isDark ? '#232946' : '#f8fafc',
                  color: isDark ? '#fff' : '#232946',
                  fontSize: 17,
                  fontWeight: 500,
                  outline: 'none',
                  boxShadow: isDark ? '0 1px 4px #23294633' : '0 1px 4px #cbd5e133',
                  transition: 'border 0.2s',
                  boxSizing: 'border-box',
                }}
              />
              <input
                type="text"
                placeholder="Subcategory"
                value={formData.subcategory}
                onChange={(e) => setFormData({ ...formData, subcategory: e.target.value })}
                style={{
                  width: '100%',
                  maxWidth: '100%',
                  padding: '16px',
                  borderRadius: 12,
                  border: `2px solid ${isDark ? '#4b5563' : '#cbd5e1'}`,
                  marginBottom: 16,
                  background: isDark ? '#232946' : '#f8fafc',
                  color: isDark ? '#fff' : '#232946',
                  fontSize: 17,
                  fontWeight: 500,
                  outline: 'none',
                  boxShadow: isDark ? '0 1px 4px #23294633' : '0 1px 4px #cbd5e133',
                  transition: 'border 0.2s',
                  boxSizing: 'border-box',
                }}
              />

              <input
                type="number"
                placeholder="Price"
                value={formData.price}
                onChange={(e) => setFormData({ ...formData, price: e.target.value === '' ? '' : parseFloat(e.target.value) })}
                min="0"
                style={{
                  width: '100%',
                  maxWidth: '100%',
                  padding: '16px',
                  borderRadius: 12,
                  border: `2px solid ${isDark ? '#4b5563' : '#cbd5e1'}`,
                  marginBottom: 16,
                  background: isDark ? '#232946' : '#f8fafc',
                  color: isDark ? '#fff' : '#232946',
                  fontSize: 17,
                  fontWeight: 500,
                  outline: 'none',
                  boxShadow: isDark ? '0 1px 4px #23294633' : '0 1px 4px #cbd5e133',
                  transition: 'border 0.2s',
                  boxSizing: 'border-box',
                }}
              />

              <input
                type="number"
                placeholder="Quantity"
                value={formData.quantity}
                onChange={(e) => setFormData({ ...formData, quantity: Number(e.target.value) })}
                min="1"
                style={{
                  width: '100%',
                  maxWidth: '100%',
                  padding: '16px',
                  borderRadius: 12,
                  border: `2px solid ${isDark ? '#4b5563' : '#cbd5e1'}`,
                  marginBottom: 16,
                  background: isDark ? '#232946' : '#f8fafc',
                  color: isDark ? '#fff' : '#232946',
                  fontSize: 17,
                  fontWeight: 500,
                  outline: 'none',
                  boxShadow: isDark ? '0 1px 4px #23294633' : '0 1px 4px #cbd5e133',
                  transition: 'border 0.2s',
                  boxSizing: 'border-box',
                }}
              />

              <input
                type="text"
                placeholder="Material"
                value={formData.material}
                onChange={(e) => setFormData({ ...formData, material: e.target.value })}
                style={{
                  width: '100%',
                  maxWidth: '100%',
                  padding: '16px',
                  borderRadius: 12,
                  border: `2px solid ${isDark ? '#4b5563' : '#cbd5e1'}`,
                  marginBottom: 16,
                  background: isDark ? '#232946' : '#f8fafc',
                  color: isDark ? '#fff' : '#232946',
                  fontSize: 17,
                  fontWeight: 500,
                  outline: 'none',
                  boxShadow: isDark ? '0 1px 4px #23294633' : '0 1px 4px #cbd5e133',
                  transition: 'border 0.2s',
                  boxSizing: 'border-box',
                }}
              />

              {/* Image Input Section */}
              <div style={{ marginBottom: '20px' }}>
                <label style={{
                  display: 'block',
                  marginBottom: '12px',
                  color: isDark ? '#ffffff' : '#000000',
                  fontSize: '16px',
                  fontWeight: '600'
                }}>
                  Product Image
                </label>

                {/* Image Preview */}
                {imagePreview && (
                  <div style={{
                    position: 'relative',
                    display: 'inline-block',
                    marginBottom: '16px',
                    marginRight: '16px',
                    borderRadius: '10px',
                    overflow: 'hidden',
                    width: '120px',
                    height: '120px',
                    border: `2px solid ${isDark ? '#4b5563' : '#e5e7eb'}`,
                  }}>
                    <img
                      src={imagePreview}
                      alt="Preview"
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'contain',
                        display: 'block',
                      }}
                    />
                    <button
                      type="button"
                      onClick={removeImage}
                      style={{
                        position: 'absolute',
                        top: '0',
                        right: '0',
                        backgroundColor: 'transparent',
                        color: '#ffffff',
                        border: 'none',
                        borderRadius: '0',
                        width: '24px',
                        height: '24px',
                        fontSize: '20px',
                        fontWeight: 'bold',
                        cursor: 'pointer',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        textShadow: '0 0 4px rgba(0, 0, 0, 0.8), 0 0 8px rgba(0, 0, 0, 0.6)',
                        transition: 'all 0.2s ease',
                        zIndex: 10,
                        lineHeight: '1',
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.color = '#ef4444';
                        e.currentTarget.style.transform = 'scale(1.2)';
                        e.currentTarget.style.textShadow = '0 0 6px rgba(0, 0, 0, 1), 0 0 12px rgba(0, 0, 0, 0.8)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.color = '#ffffff';
                        e.currentTarget.style.transform = 'scale(1)';
                        e.currentTarget.style.textShadow = '0 0 4px rgba(0, 0, 0, 0.8), 0 0 8px rgba(0, 0, 0, 0.6)';
                      }}
                    >
                      ×
                    </button>
                  </div>
                )}

                {/* Image Input Method Selector */}
                <div style={{
                  display: 'flex',
                  gap: '8px',
                  marginBottom: '12px',
                  flexWrap: 'wrap'
                }}>
                  <button
                    type="button"
                    onClick={() => setImageInputMethod('upload')}
                    style={{
                      ...methodButtonStyle,
                      backgroundColor: imageInputMethod === 'upload' ? '#3b82f6' : (isDark ? '#4b5563' : '#f3f4f6'),
                      color: imageInputMethod === 'upload' ? '#ffffff' : (isDark ? '#ffffff' : '#000000'),
                    }}
                  >
                    📁 Upload
                  </button>
                  <button
                    type="button"
                    onClick={() => setImageInputMethod('camera')}
                    style={{
                      ...methodButtonStyle,
                      backgroundColor: imageInputMethod === 'camera' ? '#3b82f6' : (isDark ? '#4b5563' : '#f3f4f6'),
                      color: imageInputMethod === 'camera' ? '#ffffff' : (isDark ? '#ffffff' : '#000000'),
                    }}
                  >
                    📷 Camera
                  </button>
                  <button
                    type="button"
                    onClick={() => setImageInputMethod('url')}
                    style={{
                      ...methodButtonStyle,
                      backgroundColor: imageInputMethod === 'url' ? '#3b82f6' : (isDark ? '#4b5563' : '#f3f4f6'),
                      color: imageInputMethod === 'url' ? '#ffffff' : (isDark ? '#ffffff' : '#000000'),
                    }}
                  >
                    🔗 URL
                  </button>
                </div>

                {/* Input Method Content */}
                {imageInputMethod === 'upload' && (
                  <div>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleFileUpload}
                      style={{
                        ...inputStyle,
                        padding: '8px',
                        width: '100%',
                        maxWidth: '100%',
                        boxSizing: 'border-box',
                        marginBottom: 8,
                      }}
                    />
                  </div>
                )}

                {imageInputMethod === 'camera' && (
                  <div>
                    <button
                      type="button"
                      onClick={handleCameraCapture}
                      style={{
                        ...inputStyle,
                        backgroundColor: '#10b981',
                        color: '#ffffff',
                        cursor: 'pointer',
                        border: 'none',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        gap: '8px',
                        width: '100%',
                        maxWidth: '100%',
                        boxSizing: 'border-box',
                        marginBottom: 8,
                      }}
                    >
                      📷 Take Photo
                    </button>
                  </div>
                )}

                {imageInputMethod === 'url' && (
                  <div>
                    <input
                      type="url"
                      placeholder="Enter image URL"
                      value={formData.image}
                      onChange={(e) => handleUrlInput(e.target.value)}
                      style={{
                        ...inputStyle,
                        width: '100%',
                        maxWidth: '100%',
                        boxSizing: 'border-box',
                        marginBottom: 8,
                      }}
                    />
                  </div>
                )}
              </div>
            </div>

            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              gap: '18px',
              marginTop: 36,
              width: '100%',
              maxWidth: '100%',
              boxSizing: 'border-box',
            }}>
              <button
                onClick={() => {
                  if (showAddModal) onCloseAddModal && onCloseAddModal();
                  setEditingProduct(null);
                  resetForm();
                }}
                style={{
                  flex: 1,
                  minWidth: 0,
                  padding: '18px',
                  borderRadius: '12px',
                  background: isDark ? '#64748b' : '#e5e7eb',
                  color: isDark ? '#fff' : '#1e293b',
                  border: 'none',
                  cursor: 'pointer',
                  fontSize: '18px',
                  fontWeight: 700,
                  boxShadow: isDark ? '0 2px 8px #11182722' : '0 2px 8px #a5b4fc22',
                  transition: 'background 0.2s',
                  maxWidth: '100%',
                  boxSizing: 'border-box',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                }}
              >
                Cancel
              </button>

              <button
                onClick={handleSave}
                style={{
                  flex: 1,
                  minWidth: 0,
                  padding: '18px',
                  borderRadius: '12px',
                  background: 'linear-gradient(90deg, #6366f1 0%, #3b82f6 100%)',
                  color: '#fff',
                  border: 'none',
                  cursor: 'pointer',
                  fontSize: '18px',
                  fontWeight: 700,
                  boxShadow: isDark ? '0 2px 8px #11182722' : '0 2px 8px #a5b4fc22',
                  transition: 'background 0.2s',
                  maxWidth: '100%',
                  boxSizing: 'border-box',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                }}
              >
                {editingProduct ? 'Update' : 'Create'}
              </button>
            </div>
          </div>
        </div>
      )}
      </div> {/* Close scrollable content area */}
      </div>
    </>
  );
};

export default ProductsScreen;